<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人简介 - 全栈开发工程师</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-text">Portfolio</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link">首页</a></li>
                <li><a href="#about" class="nav-link">关于我</a></li>
                <li><a href="#skills" class="nav-link">技能</a></li>
                <li><a href="#experience" class="nav-link">经历</a></li>
                <li><a href="#projects" class="nav-link">项目</a></li>
                <li><a href="#contact" class="nav-link">联系</a></li>
            </ul>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 主页部分 -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-avatar">
                    <img id="avatar" src="" alt="头像" class="avatar-img">
                    <div class="avatar-ring"></div>
                </div>
                <h1 class="hero-title">
                    <span class="greeting">你好，我是</span>
                    <span id="name" class="name"></span>
                </h1>
                <p id="title" class="hero-subtitle"></p>
                <p id="subtitle" class="hero-description"></p>
                <div class="hero-buttons">
                    <a href="#contact" class="btn btn-primary">联系我</a>
                    <a href="#projects" class="btn btn-secondary">查看作品</a>
                </div>
                <div class="social-links">
                    <a id="github-link" href="#" class="social-link" target="_blank">
                        <i class="fab fa-github"></i>
                    </a>
                    <a id="linkedin-link" href="#" class="social-link" target="_blank">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a id="blog-link" href="#" class="social-link" target="_blank">
                        <i class="fas fa-blog"></i>
                    </a>
                </div>
            </div>
        </div>
        <div class="scroll-indicator">
            <div class="scroll-arrow"></div>
        </div>
    </section>

    <!-- 关于我部分 -->
    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title">关于我</h2>
            <div class="about-content">
                <div class="about-text">
                    <p id="bio" class="bio-text"></p>
                    <div class="about-stats">
                        <div class="stat-item">
                            <span class="stat-number">5+</span>
                            <span class="stat-label">年经验</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">50+</span>
                            <span class="stat-label">项目完成</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">10+</span>
                            <span class="stat-label">技术栈</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 技能部分 -->
    <section id="skills" class="skills">
        <div class="container">
            <h2 class="section-title">技能专长</h2>
            <div id="skills-container" class="skills-grid">
                <!-- 技能内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </section>

    <!-- 经历部分 -->
    <section id="experience" class="experience">
        <div class="container">
            <h2 class="section-title">工作经历</h2>
            <div id="experience-container" class="timeline">
                <!-- 经历内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </section>

    <!-- 项目部分 -->
    <section id="projects" class="projects">
        <div class="container">
            <h2 class="section-title">精选项目</h2>
            <div id="projects-container" class="projects-grid">
                <!-- 项目内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </section>

    <!-- 联系部分 -->
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">联系我</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span id="email"></span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span id="phone"></span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span id="location"></span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 个人简介. 保留所有权利.</p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
